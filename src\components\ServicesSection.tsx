import React from 'react';
import ServiceCard from './ServiceCard';
import { Bot, MessageSquare, Mic, Mail, Code, Users } from 'lucide-react';

const services = [
  {
    icon: Bo<PERSON>,
    title: 'Automatización Inteligente',
    description:
      'Optimiza procesos empresariales con IA avanzada que aprende y se adapta automáticamente.',
    features: [
      'Reducción del 70% en tareas repetitivas',
      'Análisis predictivo en tiempo real',
      'Integración con sistemas existentes',
    ],
    ctaText: 'Automatizar Procesos',
    gradient: 'from-purple-500 to-pink-500',
  },
  {
    icon: MessageSquare,
    title: 'Agentes IA Personalizados',
    description:
      'Asistentes virtuales inteligentes diseñados específicamente para tu industria y necesidades.',
    features: [
      'Entrenamiento con datos específicos',
      'Respuestas contextuales avanzadas',
      'Mejora continua del rendimiento',
    ],
    ctaText: 'Crear Agente IA',
    gradient: 'from-blue-500 to-cyan-500',
  },
  {
    icon: Mic,
    title: 'Asistentes de Voz IA',
    description:
      'Interfaces de voz inteligentes disponibles 24/7 para atención al cliente y soporte técnico.',
    features: [
      'Reconocimiento de voz multiidioma',
      'Respuestas naturales y fluidas',
      'Disponibilidad 24/7 sin interrupciones',
    ],
    ctaText: 'Implementar Voz IA',
    gradient: 'from-green-500 to-teal-500',
  },
  {
    icon: Users,
    title: 'Chatbots Multicanal',
    description:
      'Soluciones de mensajería inteligente para WhatsApp, Telegram, Instagram y más plataformas.',
    features: [
      'Integración con WhatsApp Business',
      'Soporte para Telegram e Instagram',
      'Gestión unificada de conversaciones',
    ],
    ctaText: 'Conectar Redes Sociales',
    gradient: 'from-orange-500 to-red-500',
  },
  {
    icon: Mail,
    title: 'Gestión Inteligente de Correo',
    description:
      'Automatización completa del flujo de emails con clasificación y respuestas inteligentes.',
    features: [
      'Clasificación automática de emails',
      'Respuestas contextuales generadas',
      'Análisis de sentimientos avanzado',
    ],
    ctaText: 'Optimizar Email',
    gradient: 'from-indigo-500 to-purple-500',
  },
  {
    icon: Code,
    title: 'Webs con IA Integrada',
    description:
      'Desarrollo de sitios web modernos con capacidades de inteligencia artificial incorporadas.',
    features: [
      'Experiencias personalizadas por IA',
      'Optimización automática de contenido',
      'Analíticas predictivas integradas',
    ],
    ctaText: 'Desarrollar Web IA',
    gradient: 'from-yellow-500 to-orange-500',
  },
];

export default function ServicesSection() {
  return (
    <section id='servicios' className='py-20 bg-white'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='text-center mb-16'>
          <h2 className='text-4xl sm:text-5xl font-bold text-gray-900 mb-6'>
            Nuestros Servicios de IA
          </h2>
          <p className='text-xl text-gray-600 max-w-3xl mx-auto font-light'>
            Soluciones completas de inteligencia artificial diseñadas para
            transformar tu negocio y optimizar cada aspecto de tu operación
          </p>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
          {services.map((service, index) => (
            <div
              key={index}
              className='animate-fade-in-up'
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <ServiceCard {...service} />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
