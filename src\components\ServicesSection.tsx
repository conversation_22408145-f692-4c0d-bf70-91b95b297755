import React, { useEffect, useRef } from 'react';
import {
  Bot,
  MessageSquare,
  Mic,
  Mail,
  Code,
  Users,
  ArrowRight,
} from 'lucide-react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const services = [
  {
    icon: Bot,
    title: 'Automatización Inteligente',
    description:
      'Optimiza procesos empresariales con IA avanzada que aprende y se adapta automáticamente.',
    features: [
      'Reducción del 70% en tareas repetitivas',
      'Análisis predictivo en tiempo real',
      'Integración con sistemas existentes',
    ],
    ctaText: 'Automatizar Procesos',
    gradient: 'from-purple-500 to-pink-500',
    bgGradient: 'bg-gradient-to-br from-purple-50 to-pink-50',
    glowColor: 'rgba(168, 85, 247, 0.4)',
    textColor: 'text-purple-600',
    gif: '🤖',
  },
  {
    icon: MessageSquare,
    title: 'Agentes IA Personalizados',
    description:
      'Asistentes virtuales inteligentes diseñados específicamente para tu industria y necesidades.',
    features: [
      'Entrenamiento con datos específicos',
      'Respuestas contextuales avanzadas',
      'Mejora continua del rendimiento',
    ],
    ctaText: 'Crear Agente IA',
    gradient: 'from-blue-500 to-cyan-500',
    bgGradient: 'bg-gradient-to-br from-blue-50 to-cyan-50',
    glowColor: 'rgba(59, 130, 246, 0.4)',
    textColor: 'text-blue-600',
    gif: '💬',
  },
  {
    icon: Mic,
    title: 'Asistentes de Voz IA',
    description:
      'Interfaces de voz inteligentes disponibles 24/7 para atención al cliente y soporte técnico.',
    features: [
      'Reconocimiento de voz multiidioma',
      'Respuestas naturales y fluidas',
      'Disponibilidad 24/7 sin interrupciones',
    ],
    ctaText: 'Implementar Voz IA',
    gradient: 'from-green-500 to-teal-500',
    bgGradient: 'bg-gradient-to-br from-green-50 to-teal-50',
    glowColor: 'rgba(34, 197, 94, 0.4)',
    textColor: 'text-green-600',
    gif: '🎤',
  },
  {
    icon: Users,
    title: 'Chatbots Multicanal',
    description:
      'Soluciones de mensajería inteligente para WhatsApp, Telegram, Instagram y más plataformas.',
    features: [
      'Integración con WhatsApp Business',
      'Soporte para Telegram e Instagram',
      'Gestión unificada de conversaciones',
    ],
    ctaText: 'Conectar Redes Sociales',
    gradient: 'from-orange-500 to-red-500',
    bgGradient: 'bg-gradient-to-br from-orange-50 to-red-50',
    glowColor: 'rgba(249, 115, 22, 0.4)',
    textColor: 'text-orange-600',
    gif: '👥',
  },
  {
    icon: Mail,
    title: 'Gestión Inteligente de Correo',
    description:
      'Automatización completa del flujo de emails con clasificación y respuestas inteligentes.',
    features: [
      'Clasificación automática de emails',
      'Respuestas contextuales generadas',
      'Análisis de sentimientos avanzado',
    ],
    ctaText: 'Optimizar Email',
    gradient: 'from-indigo-500 to-purple-500',
    bgGradient: 'bg-gradient-to-br from-indigo-50 to-purple-50',
    glowColor: 'rgba(99, 102, 241, 0.4)',
    textColor: 'text-indigo-600',
    gif: '📧',
  },
  {
    icon: Code,
    title: 'Webs con IA Integrada',
    description:
      'Desarrollo de sitios web modernos con capacidades de inteligencia artificial incorporadas.',
    features: [
      'Experiencias personalizadas por IA',
      'Optimización automática de contenido',
      'Analíticas predictivas integradas',
    ],
    ctaText: 'Desarrollar Web IA',
    gradient: 'from-yellow-500 to-orange-500',
    bgGradient: 'bg-gradient-to-br from-yellow-50 to-orange-50',
    glowColor: 'rgba(245, 158, 11, 0.4)',
    textColor: 'text-yellow-600',
    gif: '💻',
  },
];

// Componente individual para cada sección de servicio
function ServiceSection({ service, index, isEven }) {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const descriptionRef = useRef(null);
  const featuresRef = useRef(null);
  const buttonRef = useRef(null);
  const iconRef = useRef(null);
  const emojiRef = useRef(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animación de entrada con ScrollTrigger
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      });

      // Animación del emoji flotante
      gsap.to(emojiRef.current, {
        y: -20,
        duration: 2,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
      });

      // Animación del icono con rotación suave
      gsap.to(iconRef.current, {
        rotation: 360,
        duration: 20,
        ease: 'none',
        repeat: -1,
      });

      // Secuencia de entrada
      tl.fromTo(
        titleRef.current,
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: 'power3.out' }
      )
        .fromTo(
          descriptionRef.current,
          { y: 30, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.6, ease: 'power2.out' },
          '-=0.4'
        )
        .fromTo(
          featuresRef.current,
          { y: 30, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.6, ease: 'power2.out' },
          '-=0.3'
        )
        .fromTo(
          buttonRef.current,
          { y: 30, opacity: 0, scale: 0.9 },
          { y: 0, opacity: 1, scale: 1, duration: 0.6, ease: 'back.out(1.7)' },
          '-=0.2'
        );

      // Efecto de glow pulsante en el botón
      gsap.to(buttonRef.current, {
        boxShadow: `0 0 30px ${service.glowColor}, 0 0 60px ${service.glowColor}`,
        duration: 2,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
      });
    }, sectionRef);

    return () => ctx.revert();
  }, [service.glowColor]);

  const Icon = service.icon;

  return (
    <section
      ref={sectionRef}
      className={`relative min-h-screen flex items-center py-20 ${service.bgGradient} overflow-hidden`}
    >
      {/* Elementos decorativos de fondo */}
      <div className='absolute inset-0 overflow-hidden'>
        <div
          className='absolute top-10 right-10 w-32 h-32 rounded-full opacity-20'
          style={{
            background: `radial-gradient(circle, ${service.glowColor} 0%, transparent 70%)`,
            filter: 'blur(20px)',
          }}
        />
        <div
          className='absolute bottom-20 left-10 w-24 h-24 rounded-full opacity-30'
          style={{
            background: `radial-gradient(circle, ${service.glowColor} 0%, transparent 70%)`,
            filter: 'blur(15px)',
          }}
        />
      </div>

      <div className='relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div
          className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${isEven ? 'lg:grid-flow-col-dense' : ''}`}
        >
          {/* Contenido de texto */}
          <div className={`space-y-8 ${isEven ? 'lg:col-start-2' : ''}`}>
            <div className='space-y-6'>
              <h2
                ref={titleRef}
                className={`text-4xl sm:text-5xl lg:text-6xl font-bold ${service.textColor} leading-tight`}
                style={{
                  textShadow: `0 0 20px ${service.glowColor}`,
                }}
              >
                {service.title}
              </h2>

              <p
                ref={descriptionRef}
                className='text-xl text-gray-700 leading-relaxed font-light'
              >
                {service.description}
              </p>
            </div>

            <div ref={featuresRef} className='space-y-4'>
              {service.features.map((feature, idx) => (
                <div key={idx} className='flex items-center space-x-4'>
                  <div
                    className={`w-3 h-3 rounded-full bg-gradient-to-r ${service.gradient} flex-shrink-0`}
                    style={{
                      boxShadow: `0 0 10px ${service.glowColor}`,
                    }}
                  />
                  <span className='text-gray-700 font-medium'>{feature}</span>
                </div>
              ))}
            </div>

            <button
              ref={buttonRef}
              className={`
                group relative inline-flex items-center justify-center
                px-8 py-4 text-lg font-bold text-white rounded-full
                bg-gradient-to-r ${service.gradient}
                hover:scale-105 transition-all duration-300
                border-2 border-white/20
                overflow-hidden
              `}
            >
              <span className='relative z-10 flex items-center space-x-2'>
                <span>{service.ctaText}</span>
                <ArrowRight className='h-5 w-5 transition-transform duration-300 group-hover:translate-x-1' />
              </span>

              {/* Efecto de brillo */}
              <div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700' />
            </button>
          </div>

          {/* Área visual con icono y emoji */}
          <div
            className={`relative flex items-center justify-center ${isEven ? 'lg:col-start-1' : ''}`}
          >
            <div className='relative'>
              {/* Emoji flotante */}
              <div
                ref={emojiRef}
                className='absolute -top-16 -right-16 text-6xl z-10'
              >
                {service.gif}
              </div>

              {/* Icono principal con efectos */}
              <div
                className='relative w-64 h-64 rounded-full flex items-center justify-center'
                style={{
                  background: `radial-gradient(circle, ${service.glowColor} 0%, transparent 70%)`,
                  filter: 'blur(1px)',
                }}
              >
                <div
                  ref={iconRef}
                  className={`w-48 h-48 rounded-full bg-gradient-to-r ${service.gradient} flex items-center justify-center shadow-2xl`}
                  style={{
                    boxShadow: `0 0 50px ${service.glowColor}, inset 0 0 50px rgba(255,255,255,0.1)`,
                  }}
                >
                  <Icon className='h-24 w-24 text-white' />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function ServicesSection() {
  return (
    <div id='servicios'>
      {/* Sección de introducción */}
      <section className='py-20 bg-white'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center'>
          <h2 className='text-5xl sm:text-6xl font-bold text-gray-900 mb-6 text-title-glow-blue'>
            Nuestros Servicios de IA
          </h2>
          <p className='text-xl text-gray-600 max-w-4xl mx-auto font-light leading-relaxed'>
            Soluciones completas de inteligencia artificial diseñadas para
            transformar tu negocio y optimizar cada aspecto de tu operación
          </p>
        </div>
      </section>

      {/* Secciones individuales de servicios */}
      {services.map((service, index) => (
        <ServiceSection
          key={index}
          service={service}
          index={index}
          isEven={index % 2 === 1}
        />
      ))}
    </div>
  );
}
