# 🎨 Guía de Formateo de Código

## 📋 Comandos Disponibles

### Formateo <PERSON> (<PERSON>ttier)

```bash
npm run format:quick
```

**Uso:** Antes de hacer commit para formatear rápidamente todos los archivos.

### Formateo <PERSON> (Prettier + ESLint)

```bash
npm run format:all
```

**Uso:** Para formateo completo con corrección de errores de linting.

### Solo Formateo (Prettier)

```bash
npm run format
```

**Uso:** Formatear archivos sin ejecutar ESLint.

### Verificar Formato

```bash
npm run format:check
```

**Uso:** Verificar si los archivos están correctamente formateados sin modificarlos.

### Solo Linting

```bash
npm run lint        # Solo verificar
npm run lint:fix    # Verificar y corregir automáticamente
```

## 🛠️ Herramientas Configuradas

### Prettier

- **Archivos:** `.prettierrc`, `.prettierignore`
- **Formatea:** JavaScript, TypeScript, CSS, JSON, HTML, Markdown
- **Configuración:**
  - Comillas simples
  - Punto y coma
  - Ancho de línea: 80 caracteres
  - Indentación: 2 espacios

### ESLint

- **Archivo:** `eslint.config.js`
- **Integrado con:** Prettier, React, TypeScript
- **Reglas:** Recomendadas + React Hooks + Prettier

### VS Code

- **Archivos:** `.vscode/settings.json`, `.vscode/extensions.json`
- **Formateo automático:** Al guardar y pegar
- **Extensiones recomendadas:** Prettier, ESLint, Tailwind CSS

## 🚀 Flujo de Trabajo Recomendado

### Antes de hacer commit:

```bash
npm run format:quick
```

### Para desarrollo diario:

- VS Code formateará automáticamente al guardar
- O usa `npm run format:quick` cuando necesites

### Para revisión completa:

```bash
npm run format:all
```

## 📁 Archivos que se Formatean

- **JavaScript/TypeScript:** `*.js`, `*.jsx`, `*.ts`, `*.tsx`
- **Estilos:** `*.css`
- **Configuración:** `*.json`
- **Documentación:** `*.md`
- **HTML:** `*.html`

## 🚫 Archivos Excluidos

- `node_modules/`
- `dist/`
- `build/`
- `*.log`
- Archivos de configuración del sistema

## 💡 Consejos

1. **Comando más usado:** `npm run format:quick`
2. **Para CI/CD:** `npm run format:check`
3. **Solución de problemas:** `npm run format:all`
4. **VS Code:** Instala las extensiones recomendadas para mejor experiencia

## 🔧 Personalización

Para cambiar la configuración de formateo, edita:

- **Prettier:** `.prettierrc`
- **ESLint:** `eslint.config.js`
- **VS Code:** `.vscode/settings.json`
