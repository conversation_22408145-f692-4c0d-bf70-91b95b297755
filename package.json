{"name": "ai-consultancy-landing", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,json,html,md}\" \"*.{js,jsx,ts,tsx,css,json,html,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,css,json,html,md}\" \"*.{js,jsx,ts,tsx,css,json,html,md}\"", "format:quick": "npm run format", "format:all": "npm run format && npm run lint:fix", "precommit": "npm run format:quick", "preview": "vite preview"}, "dependencies": {"gsap": "^3.13.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "lint-staged": "^16.1.2", "postcss": "^8.4.35", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}