import React from 'react';
import {
  Bot,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
} from 'lucide-react';

export default function Footer() {
  return (
    <footer id='contacto' className='bg-gray-900 text-white'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16'>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
          {/* Logo y Descripción */}
          <div className='lg:col-span-2'>
            <div className='flex items-center space-x-4 mb-4'>
              <img
                src='/src/logos/LOGO_TARATIC_V8__solo_logo.svg'
                alt='TARATIC Logo'
                className='h-8 w-8'
              />
              <img
                src='/src/logos/LOGO_TARATIC_V8__solo_letras.svg'
                alt='TARATIC'
                className='h-6'
              />
            </div>
            <p className='text-gray-300 mb-6 max-w-md'>
              Líderes en consultoría de inteligencia artificial, transformando
              negocios con soluciones innovadoras y tecnología de vanguardia.
            </p>
            <div className='flex space-x-4'>
              <a
                href='#'
                className='text-gray-400 hover:text-white transition-colors'
              >
                <Facebook className='h-6 w-6' />
              </a>
              <a
                href='#'
                className='text-gray-400 hover:text-white transition-colors'
              >
                <Twitter className='h-6 w-6' />
              </a>
              <a
                href='#'
                className='text-gray-400 hover:text-white transition-colors'
              >
                <Linkedin className='h-6 w-6' />
              </a>
              <a
                href='#'
                className='text-gray-400 hover:text-white transition-colors'
              >
                <Instagram className='h-6 w-6' />
              </a>
            </div>
          </div>

          {/* Enlaces Rápidos */}
          <div>
            <h3 className='text-lg font-semibold mb-6'>Enlaces Rápidos</h3>
            <ul className='space-y-3'>
              <li>
                <a
                  href='#servicios'
                  className='text-gray-300 hover:text-white transition-colors'
                >
                  Servicios
                </a>
              </li>
              <li>
                <a
                  href='#'
                  className='text-gray-300 hover:text-white transition-colors'
                >
                  Casos de Éxito
                </a>
              </li>
              <li>
                <a
                  href='#'
                  className='text-gray-300 hover:text-white transition-colors'
                >
                  Blog
                </a>
              </li>
              <li>
                <a
                  href='#'
                  className='text-gray-300 hover:text-white transition-colors'
                >
                  Sobre Nosotros
                </a>
              </li>
              <li>
                <a
                  href='#'
                  className='text-gray-300 hover:text-white transition-colors'
                >
                  Política de Privacidad
                </a>
              </li>
            </ul>
          </div>

          {/* Contacto */}
          <div>
            <h3 className='text-lg font-semibold mb-6'>Contacto</h3>
            <ul className='space-y-4'>
              <li className='flex items-center space-x-3'>
                <Mail className='h-5 w-5 text-blue-400' />
                <span className='text-gray-300'><EMAIL></span>
              </li>
              <li className='flex items-center space-x-3'>
                <Phone className='h-5 w-5 text-blue-400' />
                <span className='text-gray-300'>+****************</span>
              </li>
              <li className='flex items-start space-x-3'>
                <MapPin className='h-5 w-5 text-blue-400 mt-1' />
                <span className='text-gray-300'>
                  1234 Tech Street
                  <br />
                  Innovation District
                  <br />
                  San Francisco, CA 94102
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className='border-t border-gray-800 mt-12 pt-8'>
          <div className='flex flex-col md:flex-row justify-between items-center'>
            <p className='text-gray-400 text-sm mb-4 md:mb-0'>
              © 2025 Taratic.com . Todos los derechos reservados.
            </p>
            <div className='flex space-x-6 text-sm'>
              <a
                href='#'
                className='text-gray-400 hover:text-white transition-colors'
              >
                Términos de Servicio
              </a>
              <a
                href='#'
                className='text-gray-400 hover:text-white transition-colors'
              >
                Política de Privacidad
              </a>
              <a
                href='#'
                className='text-gray-400 hover:text-white transition-colors'
              >
                Cookies
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
